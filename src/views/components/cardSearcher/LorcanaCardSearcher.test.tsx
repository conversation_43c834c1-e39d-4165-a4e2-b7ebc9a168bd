import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { typeInCardSearcher } from '../../../../tests/views/components/cardSearcher/helpers';
import * as LorcanaCardsAPI from '../../../api/lorcana/LorcanaCards';
import * as LorcanaSetsAPI from '../../../api/lorcana/LorcanaSets';
import { LorcanaCardSearcher } from './LorcanaCardSearcher';

// Mock the API calls
vi.mock('../../../api/lorcana/LorcanaCards', () => ({
  search: vi.fn().mockImplementation(() => ({
    request: { abort: vi.fn() },
    promise: Promise.resolve(Immutable.List([])),
  })),
}));

vi.mock('../../../api/lorcana/LorcanaSets', () => ({
  search: vi.fn().mockImplementation(() => {
    console.trace('search');
    return {
      request: { abort: vi.fn() },
      promise: Promise.resolve(Immutable.List([])),
    };
  }),
}));

const mockChooseSuggestion = vi.fn();

const renderLorcanaCardSearcher = () => {
  return renderWithDispatcher(LorcanaCardSearcher, {
    chooseSuggestion: mockChooseSuggestion,
  });
};

describe('LorcanaCardSearcher', () => {
  describe('component render', () => {
    it('renders the search input', () => {
      renderLorcanaCardSearcher();
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });
  });

  describe('search functionality', () => {
    describe('when searching for cards', () => {
      it('calls Cards API', async () => {
        renderLorcanaCardSearcher();
        typeInCardSearcher('test');

        await vi.waitFor(() => {
          expect(LorcanaCardsAPI.search).toHaveBeenCalledWith('test', Immutable.Set());
        });
      });
    });

    describe('when searching for sets', () => {
      it('calls CardSets API when "s:" prefix is typed', async () => {
        renderLorcanaCardSearcher();
        typeInCardSearcher('s:test');

        await vi.waitFor(() => {
          expect(LorcanaSetsAPI.search).toHaveBeenCalledWith('test');
        });
      });
    });
  });
});
